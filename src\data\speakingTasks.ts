export interface SpeakingTask {
  id: string;
  part: 1 | 2 | 3;
  title: string;
  instructions: string;
  questions: string[];
  preparationTime?: number; // in seconds
  speakingTime: number; // in seconds
  tips?: string[];
}

export const SPEAKING_TASKS: Record<string, SpeakingTask[]> = {
  'Environment': [
    {
      id: 'env-speaking-part1',
      part: 1,
      title: 'Environment and Nature',
      instructions: 'The examiner will ask you questions about yourself and familiar topics like environment and nature. Answer naturally and give full responses.',
      questions: [
        'Do you think environmental protection is important? Why?',
        'What do you do to protect the environment in your daily life?',
        'Have you noticed any changes in the environment in your area?',
        'Do you prefer spending time in natural places or urban areas? Why?'
      ],
      speakingTime: 30, // 30 seconds per question
      tips: [
        'Give personal examples and experiences',
        'Explain your reasons clearly',
        'Use present tense for habits and general facts',
        'Keep your answers natural and conversational'
      ]
    },
    {
      id: 'env-speaking-part2',
      part: 2,
      title: 'Describe an Environmental Problem',
      instructions: 'You will have 1 minute to prepare and then speak for 1-2 minutes about the topic below.',
      questions: [
        'Describe an environmental problem in your area. You should say: What the problem is, How it affects people and the environment, What causes this problem, And explain what you think should be done to solve it.'
      ],
      preparationTime: 60,
      speakingTime: 120,
      tips: [
        'Use the preparation time to make notes',
        'Cover all points mentioned in the question',
        'Use past tense for experiences and present tense for descriptions',
        'Speak for the full 2 minutes if possible'
      ]
    },
    {
      id: 'env-speaking-part3',
      part: 3,
      title: 'Environmental Issues Discussion',
      instructions: 'The examiner will ask you more abstract questions related to environmental issues. Give detailed answers with examples and explanations.',
      questions: [
        'What are the main environmental challenges facing the world today?',
        'How can governments encourage people to be more environmentally responsible?',
        'Do you think individual actions can make a significant difference to environmental problems?',
        'What role should education play in environmental awareness?'
      ],
      speakingTime: 60, // 1 minute per question
      tips: [
        'Give balanced, analytical answers',
        'Use examples from different countries or situations',
        'Express and justify your opinions clearly',
        'Use complex grammatical structures'
      ]
    }
  ],
  'Technology': [
    {
      id: 'tech-speaking-part1',
      part: 1,
      title: 'Technology in Daily Life',
      instructions: 'Answer questions about technology and how you use it in your daily life.',
      questions: [
        'How often do you use technology in your daily life?',
        'What kind of technology do you find most useful?',
        'Have you learned any new technology skills recently?',
        'Do you think technology makes life easier or more complicated?'
      ],
      speakingTime: 30,
      tips: [
        'Talk about specific devices or apps you use',
        'Give concrete examples from your experience',
        'Explain both advantages and disadvantages',
        'Use present perfect for recent experiences'
      ]
    },
    {
      id: 'tech-speaking-part2',
      part: 2,
      title: 'Describe a Piece of Technology',
      instructions: 'Describe a piece of technology that you find useful. You have 1 minute to prepare.',
      questions: [
        'Describe a piece of technology that you find useful. You should say: What it is, How you use it, How often you use it, And explain why you find it useful.'
      ],
      preparationTime: 60,
      speakingTime: 120,
      tips: [
        'Choose something you know well',
        'Describe its features and functions',
        'Explain specific benefits it provides',
        'Use descriptive language and examples'
      ]
    },
    {
      id: 'tech-speaking-part3',
      part: 3,
      title: 'Technology and Society',
      instructions: 'Discuss broader issues related to technology and its impact on society.',
      questions: [
        'How has technology changed the way people communicate?',
        'What are the potential dangers of relying too much on technology?',
        'Do you think artificial intelligence will replace human workers?',
        'How might technology develop in the next 20 years?'
      ],
      speakingTime: 60,
      tips: [
        'Consider both positive and negative aspects',
        'Use hypothetical language for future predictions',
        'Compare past, present, and future situations',
        'Support your opinions with logical reasoning'
      ]
    }
  ],
  'Education': [
    {
      id: 'edu-speaking-part1',
      part: 1,
      title: 'Education and Learning',
      instructions: 'Talk about your educational experiences and learning preferences.',
      questions: [
        'What subjects did you enjoy most at school?',
        'Do you prefer learning in groups or individually?',
        'Have you taken any online courses recently?',
        'What skills would you like to learn in the future?'
      ],
      speakingTime: 30,
      tips: [
        'Share specific memories from your education',
        'Explain your learning preferences clearly',
        'Use past tense for school experiences',
        'Express future plans and aspirations'
      ]
    },
    {
      id: 'edu-speaking-part2',
      part: 2,
      title: 'Describe a Learning Experience',
      instructions: 'Describe a time when you learned something new. You have 1 minute to prepare.',
      questions: [
        'Describe a time when you learned something new. You should say: What you learned, How you learned it, Why you decided to learn it, And explain how you felt about the learning experience.'
      ],
      preparationTime: 60,
      speakingTime: 120,
      tips: [
        'Choose a specific, memorable learning experience',
        'Describe the process step by step',
        'Include your emotions and reactions',
        'Explain the impact it had on you'
      ]
    },
    {
      id: 'edu-speaking-part3',
      part: 3,
      title: 'Education Systems and Methods',
      instructions: 'Discuss education systems, teaching methods, and learning in the modern world.',
      questions: [
        'How do you think education systems could be improved?',
        'What are the advantages and disadvantages of online learning?',
        'Should education focus more on practical skills or academic knowledge?',
        'How important is it for people to continue learning throughout their lives?'
      ],
      speakingTime: 60,
      tips: [
        'Compare different education systems',
        'Discuss various teaching methodologies',
        'Consider different types of learners',
        'Use conditional language for suggestions'
      ]
    }
  ]
};

// Reuse vocabulary from writing tasks
export { WRITING_VOCABULARY as SPEAKING_VOCABULARY } from './writingTasks';
