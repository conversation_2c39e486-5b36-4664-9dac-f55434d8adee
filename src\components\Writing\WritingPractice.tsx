import React, { useState, useEffect } from 'react';
import { Card, Typography, Button, Input, Space, Progress, Row, Col, Divider, Tag } from 'antd';
import { 
  ClockCircleOutlined, 
  EditOutlined, 
  FileTextOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  EyeOutlined,
  EyeInvisibleOutlined
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

interface WritingTask {
  id: string;
  type: 'task1' | 'task2';
  title: string;
  prompt: string;
  instructions: string;
  timeLimit: number; // in minutes
  wordLimit: {
    min: number;
    max: number;
  };
  tips?: string[];
}

interface VocabularyHint {
  word: string;
  definition: string;
  example?: string;
}

interface WritingPracticeProps {
  task: WritingTask;
  topic: string;
  vocabularyHints?: VocabularyHint[];
}

const WritingPractice: React.FC<WritingPracticeProps> = ({
  task,
  topic,
  vocabularyHints = []
}) => {
  const [essay, setEssay] = useState('');
  const [timeRemaining, setTimeRemaining] = useState(task.timeLimit * 60); // in seconds
  const [timerActive, setTimerActive] = useState(false);
  const [showInstructions, setShowInstructions] = useState(true);
  const [showVocabulary, setShowVocabulary] = useState(false);
  const [wordCount, setWordCount] = useState(0);

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (timerActive && timeRemaining > 0) {
      interval = setInterval(() => {
        setTimeRemaining(prev => prev - 1);
      }, 1000);
    } else if (timeRemaining === 0) {
      setTimerActive(false);
    }
    return () => clearInterval(interval);
  }, [timerActive, timeRemaining]);

  // Word count effect
  useEffect(() => {
    const words = essay.trim().split(/\s+/).filter(word => word.length > 0);
    setWordCount(words.length);
  }, [essay]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const startTimer = () => {
    setTimerActive(true);
  };

  const pauseTimer = () => {
    setTimerActive(false);
  };

  const resetTimer = () => {
    setTimerActive(false);
    setTimeRemaining(task.timeLimit * 60);
  };

  const resetEssay = () => {
    setEssay('');
    setWordCount(0);
    resetTimer();
  };

  const getWordCountColor = () => {
    if (wordCount < task.wordLimit.min) return '#ff4d4f';
    if (wordCount > task.wordLimit.max) return '#faad14';
    return '#52c41a';
  };

  const getTimeColor = () => {
    const percentage = (timeRemaining / (task.timeLimit * 60)) * 100;
    if (percentage <= 10) return '#ff4d4f';
    if (percentage <= 25) return '#faad14';
    return '#52c41a';
  };

  return (
    <div style={{ padding: '20px' }}>
      {/* Header */}
      <Card style={{ marginBottom: '20px' }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={3} style={{ margin: 0 }}>
              <EditOutlined /> IELTS Writing {task.type.toUpperCase()} - {topic}
            </Title>
            <Text type="secondary">{task.title}</Text>
          </Col>
          <Col>
            <Space size="large">
              {/* Timer */}
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '24px', fontWeight: 'bold', color: getTimeColor() }}>
                  <ClockCircleOutlined /> {formatTime(timeRemaining)}
                </div>
                <Space>
                  <Button 
                    type="primary" 
                    icon={<PlayCircleOutlined />} 
                    onClick={startTimer}
                    disabled={timerActive || timeRemaining === 0}
                    size="small"
                  >
                    Start
                  </Button>
                  <Button 
                    icon={<PauseCircleOutlined />} 
                    onClick={pauseTimer}
                    disabled={!timerActive}
                    size="small"
                  >
                    Pause
                  </Button>
                  <Button 
                    icon={<ReloadOutlined />} 
                    onClick={resetTimer}
                    size="small"
                  >
                    Reset
                  </Button>
                </Space>
              </div>

              {/* Word Count */}
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '20px', fontWeight: 'bold', color: getWordCountColor() }}>
                  <FileTextOutlined /> {wordCount} words
                </div>
                <Text type="secondary">
                  Target: {task.wordLimit.min}-{task.wordLimit.max} words
                </Text>
              </div>
            </Space>
          </Col>
        </Row>
      </Card>

      <Row gutter={24}>
        {/* Left Column - Task and Instructions */}
        <Col span={12}>
          {/* Task Prompt */}
          <Card 
            title="Writing Task"
            style={{ marginBottom: '20px' }}
          >
            <Paragraph style={{ fontSize: '16px', lineHeight: '1.6' }}>
              {task.prompt}
            </Paragraph>
          </Card>

          {/* Instructions */}
          <Card 
            title="Instructions"
            extra={
              <Button 
                type="text"
                icon={showInstructions ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                onClick={() => setShowInstructions(!showInstructions)}
              >
                {showInstructions ? 'Hide' : 'Show'}
              </Button>
            }
            style={{ marginBottom: '20px' }}
          >
            {showInstructions && (
              <div>
                <Paragraph style={{ fontSize: '15px' }}>
                  {task.instructions}
                </Paragraph>
                {task.tips && task.tips.length > 0 && (
                  <div>
                    <Divider />
                    <Title level={5}>Tips:</Title>
                    <ul>
                      {task.tips.map((tip, index) => (
                        <li key={index} style={{ marginBottom: '8px' }}>
                          <Text>{tip}</Text>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}
          </Card>

          {/* Vocabulary Helper */}
          {vocabularyHints.length > 0 && (
            <Card 
              title="Vocabulary Helper"
              extra={
                <Button 
                  type="text"
                  icon={showVocabulary ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                  onClick={() => setShowVocabulary(!showVocabulary)}
                >
                  {showVocabulary ? 'Hide' : 'Show'}
                </Button>
              }
            >
              {showVocabulary && (
                <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
                  {vocabularyHints.map((hint, index) => (
                    <div key={index} style={{ marginBottom: '12px', padding: '8px', backgroundColor: '#f9f9f9', borderRadius: '4px' }}>
                      <Text strong style={{ color: '#1890ff' }}>{hint.word}</Text>
                      <br />
                      <Text style={{ fontSize: '14px' }}>{hint.definition}</Text>
                      {hint.example && (
                        <>
                          <br />
                          <Text italic style={{ fontSize: '13px', color: '#666' }}>
                            Example: {hint.example}
                          </Text>
                        </>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </Card>
          )}
        </Col>

        {/* Right Column - Writing Area */}
        <Col span={12}>
          <Card 
            title="Your Essay"
            extra={
              <Space>
                <Tag color={getWordCountColor()}>{wordCount} words</Tag>
                <Button 
                  danger 
                  icon={<ReloadOutlined />} 
                  onClick={resetEssay}
                  size="small"
                >
                  Clear All
                </Button>
              </Space>
            }
          >
            <TextArea
              value={essay}
              onChange={(e) => setEssay(e.target.value)}
              placeholder="Start writing your essay here..."
              style={{ 
                minHeight: '500px',
                fontSize: '16px',
                lineHeight: '1.6',
                fontFamily: 'Georgia, serif'
              }}
              showCount={false}
            />
            
            {/* Progress Bar */}
            <div style={{ marginTop: '16px' }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                Word Count Progress:
              </Text>
              <Progress 
                percent={Math.min((wordCount / task.wordLimit.max) * 100, 100)}
                strokeColor={getWordCountColor()}
                showInfo={false}
                size="small"
              />
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default WritingPractice;
