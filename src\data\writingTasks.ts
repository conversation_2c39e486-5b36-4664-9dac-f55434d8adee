export interface WritingTask {
  id: string;
  type: 'task1' | 'task2';
  title: string;
  prompt: string;
  instructions: string;
  timeLimit: number; // in minutes
  wordLimit: {
    min: number;
    max: number;
  };
  tips?: string[];
}

export interface VocabularyHint {
  word: string;
  definition: string;
  example?: string;
}

export const WRITING_TASKS: Record<string, WritingTask[]> = {
  'Environment': [
    {
      id: 'env-task1-1',
      type: 'task1',
      title: 'Climate Change Data Analysis',
      prompt: 'The chart below shows the average temperature changes in four major cities from 1990 to 2020. Summarize the information by selecting and reporting the main features, and make comparisons where relevant.',
      instructions: 'You should write at least 150 words. You should spend about 20 minutes on this task.',
      timeLimit: 20,
      wordLimit: { min: 150, max: 200 },
      tips: [
        'Start with an overview of what the chart shows',
        'Identify the most significant trends',
        'Compare and contrast the data between cities',
        'Use specific figures from the chart',
        'Don\'t give your opinion - just describe the data'
      ]
    },
    {
      id: 'env-task2-1',
      type: 'task2',
      title: 'Environmental Protection vs Economic Growth',
      prompt: 'Some people believe that environmental protection should be prioritized over economic development, while others argue that economic growth is more important for society. Discuss both views and give your own opinion.',
      instructions: 'Give reasons for your answer and include any relevant examples from your own knowledge or experience. You should write at least 250 words. You should spend about 40 minutes on this task.',
      timeLimit: 40,
      wordLimit: { min: 250, max: 350 },
      tips: [
        'Present both sides of the argument clearly',
        'Use specific examples to support your points',
        'State your own opinion clearly',
        'Use linking words to connect your ideas',
        'Write a strong conclusion that summarizes your position'
      ]
    }
  ],
  'Technology': [
    {
      id: 'tech-task1-1',
      type: 'task1',
      title: 'Internet Usage Statistics',
      prompt: 'The table below shows internet usage statistics by age group in three countries in 2023. Summarize the information by selecting and reporting the main features, and make comparisons where relevant.',
      instructions: 'You should write at least 150 words. You should spend about 20 minutes on this task.',
      timeLimit: 20,
      wordLimit: { min: 150, max: 200 },
      tips: [
        'Identify the highest and lowest usage rates',
        'Compare patterns across age groups',
        'Note any significant differences between countries',
        'Use appropriate vocabulary for describing data',
        'Organize your response logically'
      ]
    },
    {
      id: 'tech-task2-1',
      type: 'task2',
      title: 'Social Media Impact on Communication',
      prompt: 'Social media has revolutionized the way people communicate, but some argue it has had negative effects on face-to-face interaction skills. To what extent do you agree or disagree with this statement?',
      instructions: 'Give reasons for your answer and include any relevant examples from your own knowledge or experience. You should write at least 250 words. You should spend about 40 minutes on this task.',
      timeLimit: 40,
      wordLimit: { min: 250, max: 350 },
      tips: [
        'Take a clear position on the statement',
        'Provide balanced arguments',
        'Use personal examples where appropriate',
        'Consider both positive and negative aspects',
        'Maintain formal academic tone'
      ]
    }
  ],
  'Education': [
    {
      id: 'edu-task1-1',
      type: 'task1',
      title: 'University Enrollment Trends',
      prompt: 'The line graph below shows the number of students enrolled in universities in four different subjects from 2010 to 2020. Summarize the information by selecting and reporting the main features, and make comparisons where relevant.',
      instructions: 'You should write at least 150 words. You should spend about 20 minutes on this task.',
      timeLimit: 20,
      wordLimit: { min: 150, max: 200 },
      tips: [
        'Describe the overall trends for each subject',
        'Identify which subjects increased or decreased',
        'Compare the relative popularity of subjects',
        'Use appropriate language for describing trends',
        'Include specific data points'
      ]
    },
    {
      id: 'edu-task2-1',
      type: 'task2',
      title: 'Online vs Traditional Education',
      prompt: 'With the advancement of technology, online education has become increasingly popular. Some people believe that online learning is as effective as traditional classroom education, while others disagree. Discuss both views and give your opinion.',
      instructions: 'Give reasons for your answer and include any relevant examples from your own knowledge or experience. You should write at least 250 words. You should spend about 40 minutes on this task.',
      timeLimit: 40,
      wordLimit: { min: 250, max: 350 },
      tips: [
        'Discuss advantages of both online and traditional education',
        'Consider different types of learners',
        'Use examples from your experience',
        'Address potential limitations of each approach',
        'Provide a balanced conclusion'
      ]
    }
  ]
};

export const WRITING_VOCABULARY: Record<string, VocabularyHint[]> = {
  'Environment': [
    {
      word: 'sustainable',
      definition: 'able to continue over a period of time without harming the environment',
      example: 'We need to find sustainable solutions to energy production.'
    },
    {
      word: 'biodiversity',
      definition: 'the variety of plant and animal life in a particular habitat',
      example: 'The rainforest has incredible biodiversity.'
    },
    {
      word: 'carbon footprint',
      definition: 'the amount of carbon dioxide released into the atmosphere as a result of activities',
      example: 'Flying frequently increases your carbon footprint significantly.'
    },
    {
      word: 'renewable energy',
      definition: 'energy from sources that are naturally replenished',
      example: 'Solar and wind power are examples of renewable energy.'
    },
    {
      word: 'ecosystem',
      definition: 'a biological community of interacting organisms and their environment',
      example: 'Pollution can disrupt the delicate balance of an ecosystem.'
    },
    {
      word: 'conservation',
      definition: 'the protection and preservation of natural resources',
      example: 'Wildlife conservation efforts have helped save many endangered species.'
    }
  ],
  'Technology': [
    {
      word: 'innovation',
      definition: 'the introduction of new ideas, methods, or technologies',
      example: 'Technological innovation has transformed modern communication.'
    },
    {
      word: 'artificial intelligence',
      definition: 'computer systems able to perform tasks that typically require human intelligence',
      example: 'Artificial intelligence is being used in medical diagnosis.'
    },
    {
      word: 'automation',
      definition: 'the use of technology to perform tasks without human intervention',
      example: 'Factory automation has increased production efficiency.'
    },
    {
      word: 'digital divide',
      definition: 'the gap between those who have access to technology and those who don\'t',
      example: 'The digital divide affects educational opportunities in rural areas.'
    },
    {
      word: 'cybersecurity',
      definition: 'the practice of protecting systems and data from digital attacks',
      example: 'Companies invest heavily in cybersecurity measures.'
    },
    {
      word: 'virtual reality',
      definition: 'computer-generated simulation of a three-dimensional environment',
      example: 'Virtual reality is being used for training simulations.'
    }
  ],
  'Education': [
    {
      word: 'curriculum',
      definition: 'the subjects and content taught in a school or course',
      example: 'The school updated its curriculum to include coding classes.'
    },
    {
      word: 'pedagogy',
      definition: 'the method and practice of teaching',
      example: 'Modern pedagogy emphasizes student-centered learning.'
    },
    {
      word: 'literacy',
      definition: 'the ability to read and write',
      example: 'Digital literacy is becoming increasingly important.'
    },
    {
      word: 'assessment',
      definition: 'the evaluation of student learning and progress',
      example: 'Continuous assessment provides better feedback than final exams alone.'
    },
    {
      word: 'inclusive education',
      definition: 'education that includes students with diverse needs and backgrounds',
      example: 'Inclusive education benefits all students in the classroom.'
    },
    {
      word: 'critical thinking',
      definition: 'the objective analysis and evaluation of information',
      example: 'Education should develop students\' critical thinking skills.'
    }
  ]
};
