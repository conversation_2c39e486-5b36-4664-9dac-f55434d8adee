import React, { useState } from 'react';
import { Card, Button, Typography, Space, Tag, Tooltip, Modal, Row, Col, Divider } from 'antd';
import { 
  BookOutlined, 
  EyeOutlined, 
  EyeInvisibleOutlined, 
  QuestionCircleOutlined,
  ClockCircleOutlined,
  BulbOutlined
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;

interface VocabularyHint {
  word: string;
  definition: string;
  example?: string;
}

interface ReadingQuestion {
  id: string;
  question: string;
  type: 'multiple-choice' | 'true-false' | 'fill-blank' | 'short-answer';
  options?: string[];
  answer: string;
  explanation?: string;
  answerLocation?: {
    startText: string;
    endText: string;
  };
}

interface ReadingPassageProps {
  title: string;
  topic: string;
  passage: string;
  vocabularyHints: VocabularyHint[];
  questions: ReadingQuestion[];
  timeLimit?: number; // in minutes
}

const ReadingPassage: React.FC<ReadingPassageProps> = ({
  title,
  topic,
  passage,
  vocabularyHints,
  questions,
  timeLimit = 20
}) => {
  const [showAnswers, setShowAnswers] = useState(false);
  const [revealedAnswers, setRevealedAnswers] = useState<Set<number>>(new Set());
  const [highlightedAnswers, setHighlightedAnswers] = useState<Set<number>>(new Set());
  const [showVocabulary, setShowVocabulary] = useState(true); // Default to true for better learning
  const [selectedWord, setSelectedWord] = useState<VocabularyHint | null>(null);
  const [highlightedWords, setHighlightedWords] = useState<Set<string>>(new Set());
  const [timeRemaining, setTimeRemaining] = useState(timeLimit * 60); // in seconds
  const [timerActive, setTimerActive] = useState(false);

  // Timer functionality
  React.useEffect(() => {
    let interval: NodeJS.Timeout;
    if (timerActive && timeRemaining > 0) {
      interval = setInterval(() => {
        setTimeRemaining(time => time - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [timerActive, timeRemaining]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const startTimer = () => {
    setTimerActive(true);
  };

  const stopTimer = () => {
    setTimerActive(false);
  };

  const resetTimer = () => {
    setTimerActive(false);
    setTimeRemaining(timeLimit * 60);
  };

  const highlightVocabulary = (text: string) => {
    let highlightedText = text;

    // First, highlight answer locations if any are selected
    highlightedAnswers.forEach(questionIndex => {
      const question = questions[questionIndex];
      if (question?.answerLocation) {
        const { startText, endText } = question.answerLocation;
        const startIndex = highlightedText.toLowerCase().indexOf(startText.toLowerCase());
        const endIndex = highlightedText.toLowerCase().indexOf(endText.toLowerCase(), startIndex);

        if (startIndex !== -1 && endIndex !== -1) {
          const beforeText = highlightedText.substring(0, startIndex);
          const highlightedSection = highlightedText.substring(startIndex, endIndex + endText.length);
          const afterText = highlightedText.substring(endIndex + endText.length);

          highlightedText = beforeText +
            `<span class="answer-highlight" data-question="${questionIndex}">${highlightedSection}</span>` +
            afterText;
        }
      }
    });

    // Then highlight vocabulary words (including variations)
    vocabularyHints.forEach(hint => {
      // Create variations of the word (plural, different forms)
      const wordVariations = [
        hint.word,
        hint.word + 's',
        hint.word + 'es',
        hint.word.endsWith('y') ? hint.word.slice(0, -1) + 'ies' : '',
        hint.word.endsWith('e') ? hint.word.slice(0, -1) + 'ing' : hint.word + 'ing',
        hint.word + 'ed',
        hint.word.endsWith('e') ? hint.word + 'd' : '',
      ].filter(variation => variation.length > 2); // Filter out very short variations

      wordVariations.forEach(variation => {
        const regex = new RegExp(`\\b${variation}\\b`, 'gi');
        highlightedText = highlightedText.replace(regex, (match) => {
          // Don't highlight if already inside an answer highlight or already highlighted
          if (match.includes('<span')) return match;
          return `<span class="vocabulary-word" data-word="${hint.word.toLowerCase()}">${match}</span>`;
        });
      });
    });

    return highlightedText;
  };

  const handleWordClick = (word: string) => {
    const hint = vocabularyHints.find(h => h.word.toLowerCase() === word.toLowerCase());
    if (hint) {
      setSelectedWord(hint);
      setHighlightedWords(prev => new Set([...Array.from(prev), word.toLowerCase()]));
    }
  };

  // Toggle individual answer reveal
  const toggleAnswerReveal = (questionIndex: number) => {
    const newRevealed = new Set(revealedAnswers);
    if (newRevealed.has(questionIndex)) {
      newRevealed.delete(questionIndex);
    } else {
      newRevealed.add(questionIndex);
    }
    setRevealedAnswers(newRevealed);
  };

  // Toggle all answers
  const toggleAllAnswers = () => {
    if (showAnswers) {
      setShowAnswers(false);
      setRevealedAnswers(new Set());
      setHighlightedAnswers(new Set());
    } else {
      setShowAnswers(true);
      setRevealedAnswers(new Set(questions.map((_, index) => index)));
    }
  };

  // Toggle answer highlighting
  const toggleAnswerHighlight = (questionIndex: number) => {
    const newHighlighted = new Set(highlightedAnswers);
    if (newHighlighted.has(questionIndex)) {
      newHighlighted.delete(questionIndex);
    } else {
      newHighlighted.add(questionIndex);
    }
    setHighlightedAnswers(newHighlighted);
  };

  const renderPassage = () => {
    const highlightedPassage = (showVocabulary || highlightedAnswers.size > 0) ? highlightVocabulary(passage) : passage;

    return (
      <div
        style={{
          fontSize: '18px',
          lineHeight: '1.8',
          textAlign: 'justify',
          padding: '20px',
          backgroundColor: '#fafafa',
          borderRadius: '8px'
        }}
        dangerouslySetInnerHTML={{ __html: highlightedPassage }}
        onClick={(e) => {
          const target = e.target as HTMLElement;
          if (target.classList.contains('vocabulary-word')) {
            const word = target.getAttribute('data-word');
            if (word) handleWordClick(word);
          }
        }}
      />
    );
  };

  const renderQuestions = () => {
    return questions.map((question, index) => (
      <Card key={question.id} style={{ marginBottom: '16px' }}>
        <div style={{ marginBottom: '12px', display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <div style={{ flex: 1 }}>
            <Text strong style={{ fontSize: '16px' }}>
              Question {index + 1}: {question.question}
            </Text>
          </div>
          <Space size="small">
            {question.answerLocation && (
              <Button
                type="text"
                size="small"
                icon={<BulbOutlined />}
                onClick={() => toggleAnswerHighlight(index)}
                style={{
                  color: highlightedAnswers.has(index) ? '#52c41a' : undefined,
                  backgroundColor: highlightedAnswers.has(index) ? '#f6ffed' : undefined
                }}
              >
                {highlightedAnswers.has(index) ? 'Hide' : 'Show'} in Text
              </Button>
            )}
            <Button
              type="text"
              size="small"
              icon={revealedAnswers.has(index) ? <EyeInvisibleOutlined /> : <EyeOutlined />}
              onClick={() => toggleAnswerReveal(index)}
            >
              {revealedAnswers.has(index) ? 'Hide' : 'Show'}
            </Button>
          </Space>
        </div>

        {question.type === 'multiple-choice' && question.options && (
          <div style={{ marginBottom: '12px' }}>
            {question.options.map((option, optIndex) => (
              <div key={optIndex} style={{ margin: '8px 0' }}>
                <Text style={{ fontSize: '15px' }}>
                  {String.fromCharCode(65 + optIndex)}. {option}
                </Text>
              </div>
            ))}
          </div>
        )}

        {revealedAnswers.has(index) && (
          <div style={{
            marginTop: '12px',
            padding: '12px',
            backgroundColor: '#f6ffed',
            border: '1px solid #b7eb8f',
            borderRadius: '6px'
          }}>
            <Text strong style={{ color: '#52c41a', fontSize: '16px' }}>
              Answer: {question.answer}
            </Text>
            {question.explanation && (
              <div style={{ marginTop: '8px' }}>
                <Text style={{ fontSize: '14px', color: '#666' }}>
                  Explanation: {question.explanation}
                </Text>
              </div>
            )}
          </div>
        )}
      </Card>
    ));
  };

  return (
    <div style={{ padding: '20px' }}>
      <div style={{ marginBottom: '20px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <Title level={2}>
            <BookOutlined /> {title}
          </Title>
          <Tag color="green">{topic}</Tag>
        </div>
        
        <Space>
          <div style={{ 
            padding: '8px 16px', 
            backgroundColor: timeRemaining < 300 ? '#fff2e8' : '#f6ffed',
            border: `1px solid ${timeRemaining < 300 ? '#ffbb96' : '#b7eb8f'}`,
            borderRadius: '6px',
            display: 'flex',
            alignItems: 'center'
          }}>
            <ClockCircleOutlined style={{ marginRight: '8px' }} />
            <Text strong style={{ fontSize: '16px', color: timeRemaining < 300 ? '#d4380d' : '#52c41a' }}>
              {formatTime(timeRemaining)}
            </Text>
          </div>
          <Button onClick={timerActive ? stopTimer : startTimer}>
            {timerActive ? 'Pause' : 'Start'} Timer
          </Button>
          <Button onClick={resetTimer}>Reset</Button>
        </Space>
      </div>

      <Row gutter={24}>
        <Col span={16}>
          <Card 
            title="Reading Passage"
            extra={
              <Space>
                <Button 
                  type="text"
                  icon={<BulbOutlined />}
                  onClick={() => setShowVocabulary(!showVocabulary)}
                >
                  {showVocabulary ? 'Hide' : 'Show'} Vocabulary Hints
                </Button>
              </Space>
            }
          >
            {renderPassage()}
            
            {showVocabulary && (
              <div style={{ marginTop: '20px', padding: '16px', backgroundColor: '#e6f7ff', borderRadius: '6px' }}>
                <Text type="secondary" style={{ display: 'block', marginBottom: '8px' }}>
                  <QuestionCircleOutlined /> Click on highlighted words to see definitions and examples.
                </Text>
                <Text type="secondary" style={{ fontSize: '14px' }}>
                  <BulbOutlined /> {vocabularyHints.length} vocabulary words are highlighted in this passage to enhance your learning.
                </Text>
              </div>
            )}
          </Card>
        </Col>

        <Col span={8}>
          <Card
            title="Questions"
            extra={
              <Button
                type="text"
                icon={showAnswers ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                onClick={toggleAllAnswers}
                size="small"
              >
                {showAnswers ? 'Hide All' : 'Show All'} Answers
              </Button>
            }
          >
            {renderQuestions()}
          </Card>
        </Col>
      </Row>

      {/* Vocabulary Modal */}
      <Modal
        title={selectedWord?.word}
        open={!!selectedWord}
        onCancel={() => setSelectedWord(null)}
        footer={null}
        width={500}
      >
        {selectedWord && (
          <div>
            <Paragraph style={{ fontSize: '16px', marginBottom: '16px' }}>
              <Text strong>Definition:</Text> {selectedWord.definition}
            </Paragraph>
            {selectedWord.example && (
              <Paragraph style={{ fontSize: '16px' }}>
                <Text strong>Example:</Text> <Text italic>"{selectedWord.example}"</Text>
              </Paragraph>
            )}
          </div>
        )}
      </Modal>

      <style>{`
        .vocabulary-word {
          background-color: #fff7e6;
          border-bottom: 2px solid #ffa940;
          cursor: pointer;
          padding: 2px 4px;
          border-radius: 3px;
          transition: all 0.3s ease;
        }

        .vocabulary-word:hover {
          background-color: #ffe7ba;
          border-bottom-color: #fa8c16;
        }
      `}</style>
    </div>
  );
};

export default ReadingPassage;
