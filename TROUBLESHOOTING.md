# IELTS Platform Troubleshooting Guide

## Quick Start (If you see errors)

### Method 1: Use the Startup Script
1. Double-click `start-platform.bat` in the project folder
2. Wait for both windows to open (development server + Electron app)

### Method 2: Manual Start
1. Open Command Prompt or PowerShell in the project folder
2. Run: `npm start` (keep this window open)
3. Open a new Command Prompt/PowerShell in the same folder
4. Run: `npm run electron`

## Common Error Solutions

### Error: "Compiled with problems" or TypeScript errors
**Solution**: The TypeScript configuration has been fixed. Restart the application:
1. Close all Command Prompt/PowerShell windows
2. Use Method 1 or 2 above to restart

### Error: "Port 3001 already in use"
**Solution**: 
1. Close all Command Prompt/PowerShell windows
2. Wait 10 seconds
3. Restart using Method 1 or 2 above

### Error: "Module build failed"
**Solution**:
1. Delete the `node_modules` folder
2. Run: `npm install`
3. Restart using Method 1 or 2 above

### Error: Electron window is blank or not loading
**Solution**:
1. Make sure the development server (npm start) is running first
2. Wait 5-10 seconds after starting the server
3. Then start Electron with: `npm run electron`

## How to Use the Platform

### 1. Navigation
- **Sidebar**: Click on skills (Listening, Reading, Vocabulary)
- **Topic Grid**: Select from 10 IELTS topics
- **Presentation Mode**: Click button in top-right for classroom display

### 2. Listening Module
- **Play/Pause**: Large button in center
- **Skip**: Use backward/forward buttons (10-second jumps)
- **Transcript**: Click "Show Transcript" to reveal text
- **Answers**: Click "Show Answers" to reveal answer key
- **Navigation**: Click on transcript segments to jump to that time

### 3. Reading Module
- **Vocabulary Hints**: Click "Show Vocabulary Hints" to highlight words
- **Word Definitions**: Click highlighted words for definitions
- **Timer**: Use Start/Pause/Reset for timed practice
- **Answers**: Click "Show Answers" to reveal answer key

### 4. Vocabulary Management
- **Add Words**: Click "Add Vocabulary" button
- **Filter**: Use dropdown to filter by CEFR level (B2-C2)
- **Export**: Click "Export List" to create student handouts
- **Edit/Delete**: Use buttons next to each vocabulary item

### 5. Exporting Student Materials
- **Vocabulary Lists**: Click "Export Handout" in vocabulary section
- **Simple Text Format**: Files are plain text, suitable for printing
- **Automatic Download**: Files save to your Downloads folder

## Classroom Tips

### For Smartboard/HDMI Presentation
1. Click "Enter Presentation Mode" (top-right button)
2. Use large buttons and controls designed for touch
3. Font sizes are optimized for classroom viewing
4. Use progressive disclosure (show/hide answers) for teaching

### For 23 Students
- This is a teacher-led platform (no individual student accounts)
- Project on main screen for whole class
- Export handouts for individual student practice
- Use timer for group activities

### Best Practices
- Test audio before class (listening exercises)
- Prepare vocabulary lists in advance
- Export handouts before class starts
- Use presentation mode for better visibility

## Technical Requirements

### Minimum System Requirements
- Windows 10 or later
- 4GB RAM
- 1GB free disk space
- Audio output capability

### Recommended for Classroom
- Windows 11
- 8GB RAM
- External speakers or sound system
- Large display (projector/smartboard)
- Touch-capable display (optional)

## Getting Help

### If Problems Persist
1. Check that Node.js is installed (version 16+)
2. Ensure all files are in the correct project folder
3. Try running `npm install` to reinstall dependencies
4. Restart your computer if issues continue

### File Locations
- **Project Folder**: Contains all application files
- **Exported Handouts**: Usually in Downloads folder
- **Audio Files**: Place in `public/audio/` folder (if adding custom content)

### Performance Issues
- Close other applications to free up memory
- Use wired internet connection for stability
- Disable Windows animations for better performance
- Use hardware acceleration if available

---

**Remember**: This platform is designed for teacher-led classroom instruction. All features are optimized for presentation and group learning rather than individual student use.
