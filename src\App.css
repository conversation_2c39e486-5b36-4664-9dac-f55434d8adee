/* Global styles for IELTS Teaching Platform */

.ant-layout {
  background: #f0f2f5;
}

.ant-layout-content {
  background: #fff;
}

/* Large text for classroom visibility */
.classroom-text {
  font-size: 24px !important;
  line-height: 1.6;
}

.classroom-title {
  font-size: 36px !important;
  font-weight: bold;
}

/* Interactive elements for touch/click */
.interactive-button {
  min-height: 60px;
  font-size: 18px;
  border-radius: 8px;
}

/* Topic cards styling */
.topic-card {
  transition: all 0.3s ease;
  border-radius: 12px;
}

.topic-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

/* Presentation mode styles */
.presentation-mode {
  background: #000 !important;
  color: #fff !important;
}

.presentation-mode .ant-layout {
  background: #000 !important;
}

.presentation-mode .ant-layout-content {
  background: #000 !important;
  color: #fff !important;
}

.presentation-mode .ant-layout-header {
  background: #1a1a1a !important;
  border-bottom: 1px solid #333 !important;
}

.presentation-mode .ant-layout-sider {
  background: #1a1a1a !important;
}

.presentation-mode .ant-card {
  background: #1a1a1a !important;
  border: 1px solid #333 !important;
  color: #fff !important;
}

.presentation-mode .ant-card-head {
  background: #2a2a2a !important;
  border-bottom: 1px solid #333 !important;
  color: #fff !important;
}

.presentation-mode .ant-card-head-title {
  color: #fff !important;
}

.presentation-mode .ant-card-body {
  background: #1a1a1a !important;
  color: #fff !important;
}

.presentation-mode .ant-typography {
  color: #fff !important;
}

.presentation-mode .ant-typography h1,
.presentation-mode .ant-typography h2,
.presentation-mode .ant-typography h3,
.presentation-mode .ant-typography h4 {
  color: #fff !important;
}

.presentation-mode .ant-btn {
  background: #333 !important;
  border-color: #555 !important;
  color: #fff !important;
}

.presentation-mode .ant-btn-primary {
  background: #1890ff !important;
  border-color: #1890ff !important;
}

.presentation-mode .ant-btn:hover {
  background: #555 !important;
  border-color: #777 !important;
}

.presentation-mode .ant-btn-primary:hover {
  background: #40a9ff !important;
  border-color: #40a9ff !important;
}

/* Presentation mode specific sizing */
.presentation-mode .classroom-text {
  font-size: 28px !important;
  line-height: 1.8 !important;
}

.presentation-mode .classroom-title {
  font-size: 42px !important;
  font-weight: bold !important;
}

.presentation-mode .ant-card-head-title {
  font-size: 24px !important;
}

.presentation-mode .vocabulary-word {
  background: #ffd700 !important;
  color: #000 !important;
  padding: 2px 6px !important;
  border-radius: 4px !important;
  font-weight: bold !important;
}

.presentation-mode .answer-revealed {
  background: #52c41a !important;
  color: #000 !important;
  padding: 4px 12px !important;
  border-radius: 6px !important;
  font-weight: bold !important;
  font-size: 18px !important;
}

/* Audio player controls */
.audio-controls {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  margin: 20px 0;
}

/* Vocabulary list styling */
.vocabulary-item {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 18px;
}

.vocabulary-item:last-child {
  border-bottom: none;
}

/* Answer reveal styling */
.answer-hidden {
  background: #f0f0f0;
  color: transparent;
  user-select: none;
  cursor: pointer;
  border-radius: 4px;
  padding: 2px 8px;
}

.answer-revealed {
  background: #e6f7ff;
  color: #1890ff;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
}

/* Answer highlighting in text */
.answer-highlight {
  background: linear-gradient(120deg, #a8e6cf 0%, #dcedc1 100%);
  padding: 3px 6px;
  border-radius: 4px;
  border: 2px solid #52c41a;
  font-weight: 600;
  color: #135200;
  box-shadow: 0 2px 4px rgba(82, 196, 26, 0.2);
  animation: highlight-pulse 2s ease-in-out;
}

@keyframes highlight-pulse {
  0% { box-shadow: 0 2px 4px rgba(82, 196, 26, 0.2); }
  50% { box-shadow: 0 4px 12px rgba(82, 196, 26, 0.4); }
  100% { box-shadow: 0 2px 4px rgba(82, 196, 26, 0.2); }
}

/* Vocabulary word styling */
.vocabulary-word {
  background: #fff2e8;
  color: #d46b08;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #ffbb96;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.vocabulary-word:hover {
  background: #ffd591;
  border-color: #ff9c6e;
  transform: translateY(-1px);
}

/* Responsive design for different screen sizes */
@media (max-width: 768px) {
  .classroom-text {
    font-size: 20px !important;
  }
  
  .classroom-title {
    font-size: 28px !important;
  }
}

/* High contrast mode for better visibility */
.high-contrast {
  filter: contrast(150%);
}

/* Focus styles for accessibility */
.ant-btn:focus,
.ant-card:focus {
  outline: 3px solid #1890ff;
  outline-offset: 2px;
}
