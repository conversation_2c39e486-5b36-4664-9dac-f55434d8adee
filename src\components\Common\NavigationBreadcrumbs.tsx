import React from 'react';
import { Breadcrumb, Button } from 'antd';
import { HomeOutlined, ArrowLeftOutlined } from '@ant-design/icons';

interface BreadcrumbItem {
  title: string;
  onClick?: () => void;
  icon?: React.ReactNode;
}

interface NavigationBreadcrumbsProps {
  items: BreadcrumbItem[];
  onBack?: () => void;
  presentationMode?: boolean;
}

const NavigationBreadcrumbs: React.FC<NavigationBreadcrumbsProps> = ({
  items,
  onBack,
  presentationMode = false
}) => {
  const breadcrumbItems = items.map((item, index) => ({
    title: (
      <span 
        style={{ 
          cursor: item.onClick ? 'pointer' : 'default',
          fontSize: presentationMode ? '20px' : '16px',
          fontWeight: index === items.length - 1 ? 'bold' : 'normal',
          color: presentationMode ? '#fff' : undefined
        }}
        onClick={item.onClick}
      >
        {item.icon && <span style={{ marginRight: '8px' }}>{item.icon}</span>}
        {item.title}
      </span>
    )
  }));

  return (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      gap: '16px',
      padding: presentationMode ? '16px 24px' : '12px 0',
      background: presentationMode ? 'rgba(255, 255, 255, 0.1)' : 'transparent',
      borderRadius: presentationMode ? '8px' : '0',
      marginBottom: '16px'
    }}>
      {onBack && (
        <Button
          type="text"
          icon={<ArrowLeftOutlined />}
          onClick={onBack}
          size={presentationMode ? 'large' : 'middle'}
          style={{
            fontSize: presentationMode ? '18px' : '14px',
            color: presentationMode ? '#fff' : undefined,
            border: presentationMode ? '1px solid rgba(255, 255, 255, 0.3)' : 'none'
          }}
        >
          Back
        </Button>
      )}
      
      <Breadcrumb
        items={breadcrumbItems}
        separator={
          <span style={{ 
            color: presentationMode ? '#fff' : undefined,
            fontSize: presentationMode ? '18px' : '14px'
          }}>
            /
          </span>
        }
        style={{
          fontSize: presentationMode ? '20px' : '16px'
        }}
      />
    </div>
  );
};

export default NavigationBreadcrumbs;
